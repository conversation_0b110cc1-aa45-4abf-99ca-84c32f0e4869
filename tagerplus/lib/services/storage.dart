import 'package:get/get.dart';
import 'package:get_secure_storage/get_secure_storage.dart';

class LocalStorageService extends GetxService {
  static LocalStorageService get to => Get.find();

  final _box = GetSecureStorage(
    password: '********************************',
    container: 'tagerplus_storage',
  );

  Future<void> clear() async {
    await _box.erase();
  }

  @override
  void onClose() {
    _box.save();
    super.onClose();
  }

  String? get token => _box.read('token');
  set token(String value) => _box.write('token', value);

  String? get user => _box.read('user');
  set user(String value) => _box.write('user', value);
}
