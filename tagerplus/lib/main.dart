import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_secure_storage/get_secure_storage.dart';
import 'package:tagerplus/core/themes/app_themes.dart';
import 'package:tagerplus/pages/auth/login/login.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetSecureStorage.init(
    password: '19af010eff39553f712e175d0bdcab40',
    container: 'tagerplus_storage',
  );
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'TagerPlus',
      theme: AppTheme.defaultTheme,
      home: const LoginPage(),
    );
  }
}
